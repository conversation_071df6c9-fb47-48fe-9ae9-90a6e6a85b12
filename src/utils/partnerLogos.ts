/**
 * Utility functions for partner logo ordering and sorting
 */

export interface PartnerLogoWithOrder {
  _id: string
  name: string
  homepageOrder?: number
  aboutUsOrder?: number
  [key: string]: any
}

/**
 * Sort partner logos by their display order for a specific location
 * Falls back to alphabetical sorting by name if no order is specified
 */
export function sortPartnerLogos(
  logos: PartnerLogoWithOrder[],
  location: 'homepage' | 'about-us'
): PartnerLogoWithOrder[] {
  const orderField = location === 'homepage' ? 'homepageOrder' : 'aboutUsOrder'
  
  return [...logos].sort((a, b) => {
    const orderA = a[orderField]
    const orderB = b[orderField]
    
    // If both have order values, sort by order
    if (typeof orderA === 'number' && typeof orderB === 'number') {
      return orderA - orderB
    }
    
    // If only one has an order value, prioritize it
    if (typeof orderA === 'number' && typeof orderB !== 'number') {
      return -1
    }
    
    if (typeof orderA !== 'number' && typeof orderB === 'number') {
      return 1
    }
    
    // If neither has an order value, sort alphabetically by name
    return a.name.localeCompare(b.name)
  })
}

/**
 * Filter and sort partner logos for a specific display location
 */
export function getPartnerLogosForLocation(
  logos: PartnerLogoWithOrder[],
  location: 'homepage' | 'about-us'
): PartnerLogoWithOrder[] {
  const filtered = logos.filter(logo => 
    logo.displayLocations && logo.displayLocations.includes(location)
  )
  
  return sortPartnerLogos(filtered, location)
}
