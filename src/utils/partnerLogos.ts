/**
 * Utility functions for partner logo ordering and sorting
 */

export interface PartnerLogoWithOrder {
  _id: string
  name: string
  homepageOrder?: number
  aboutUsOrder?: number
  [key: string]: any
}

// Fisher-Yates shuffle algorithm
function shuffleArray<T>(array: T[]): T[] {
  const shuffled = [...array]
  for (let i = shuffled.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1))
    ;[shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]]
  }
  return shuffled
}

/**
 * Sort partner logos by their display order for a specific location
 * Ordered logos first (numerically), then randomized unordered logos
 */
export function sortPartnerLogos(
  logos: PartnerLogoWithOrder[],
  location: 'homepage' | 'about-us'
): PartnerLogoWithOrder[] {
  const orderField = location === 'homepage' ? 'homepageOrder' : 'aboutUsOrder'

  const ordered = logos.filter(logo => typeof logo[orderField] === 'number')
  const unordered = logos.filter(logo => typeof logo[orderField] !== 'number')

  // Sort ordered logos numerically
  ordered.sort((a, b) => (a[orderField] || 0) - (b[orderField] || 0))

  // Randomize unordered logos
  const randomizedUnordered = shuffleArray(unordered)

  // Combine: ordered first, then randomized unordered
  return [...ordered, ...randomizedUnordered]
}

/**
 * Filter and sort partner logos for a specific display location
 */
export function getPartnerLogosForLocation(
  logos: PartnerLogoWithOrder[],
  location: 'homepage' | 'about-us'
): PartnerLogoWithOrder[] {
  const filtered = logos.filter(logo => 
    logo.displayLocations && logo.displayLocations.includes(location)
  )
  
  return sortPartnerLogos(filtered, location)
}
