// src/sanity/lib/queries.ts

import { defineQuery } from "next-sanity";
import { groq } from "next-sanity";

export const POSTS_QUERY =
  defineQuery(`*[_type == "post" && defined(slug.current)][0...12]{
  _id, 
  title, 
  slug, 
  description, 
  author->{name, _type, _id},
  mainImage
}`);

export const POST_QUERY =
  defineQuery(`*[_type == "post" && slug.current == $slug][0]{
  _id,
  _type,
  title,
  description,
  body,
  mainImage,
  author->{name, _type, _id},
  relatedPosts->{[]{
    _key, // required for drag and drop
    ...@->{_id, title, slug} // get fields from the referenced post
  }
}`);

export const HOME_QUERY = groq`*[_type == "home"][0] {
  hero {
    backgroundImage,
    title,
    description
  },
  newsCta {
    title,
    buttonText,
    buttonLink
  },
  "partnerLogos": *[_type == "partnerLogo" && "homepage" in displayLocations] {
    _id,
    name,
    "logo": logo.asset->,
    url,
    displayLocations,
    homepageOrder,
    aboutUsOrder
  },
  workWithUsCards[] {
    image,
    title,
    description,
    buttonText,
    buttonLink
  },
  aboutVideo {
    videoUrl,
    thumbnailImage
  },
  letsTalk {
    backgroundImage,
    title,
    description,
    buttonText,
    buttonLink
  },
  footerLinks[] {
    text,
    url
  }
}`

export const EVENTS_QUERY = groq`{
  "data": *[_type == "eventsPage"][0] {
    hero {
      backgroundImage,
      title,
      description
    },
    video {
      thumbnailImage,
      videoUrl
    },
    venues[] {
      _key,
      name,
      logo
    }
  }
}`
