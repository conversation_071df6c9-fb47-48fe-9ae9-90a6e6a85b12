import { defineType, defineField } from 'sanity'

export const partnerLogoType = defineType({
  name: 'partner<PERSON><PERSON>',
  title: 'Partner Logo',
  type: 'document',
  fields: [
    defineField({
      name: 'name',
      title: 'Partner Name',
      type: 'string',
      validation: Rule => Rule.required(),
    }),
    defineField({
      name: 'logo',
      title: 'Logo',
      type: 'image',
      options: {
        hotspot: true,
      },
      validation: Rule => Rule.required(),
    }),
    defineField({
      name: 'url',
      title: 'Website URL',
      type: 'url',
      description: 'Partner\'s website URL',
    }),
    defineField({
      name: 'displayLocations',
      title: 'Display Locations',
      type: 'array',
      of: [{ type: 'string' }],
      options: {
        list: [
          { title: 'Homepage Carousel', value: 'homepage' },
          { title: 'About Us Page', value: 'about-us' },
        ],
      },
      validation: Rule => Rule.required(),
    }),
    defineField({
      name: 'logoWidth',
      title: 'Logo Width',
      type: 'number',
      description: 'Width of the logo in pixels',
      validation: Rule => Rule.positive().integer(),
    }),
    defineField({
      name: 'logoHeight',
      title: 'Logo Height',
      type: 'number',
      description: 'Height of the logo in pixels',
      validation: Rule => Rule.positive().integer(),
    }),
    defineField({
      name: 'homepageOrder',
      title: 'Homepage Display Order',
      type: 'number',
      description: 'Order in which this logo appears on the homepage (lower numbers appear first). Leave empty to use alphabetical order.',
      validation: Rule => Rule.integer().min(0),
    }),
    defineField({
      name: 'aboutUsOrder',
      title: 'About Us Page Display Order',
      type: 'number',
      description: 'Order in which this logo appears on the About Us page (lower numbers appear first). Leave empty to use alphabetical order.',
      validation: Rule => Rule.integer().min(0),
    }),
  ],
  preview: {
    select: {
      title: 'name',
      media: 'logo',
    },
  },
}) 