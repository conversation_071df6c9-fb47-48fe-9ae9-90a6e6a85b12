import { groq } from 'next-sanity'

export interface PartnerLogo {
  _id: string
  name: string
  logo: {
    _id: string
    url: string
  }
  url?: string
  displayLocations: string[]
  logoWidth?: number
  logoHeight?: number
  homepageOrder?: number
  aboutUsOrder?: number
}

export const getPartnerLogos = groq`
  *[_type == "partnerLogo"] {
    _id,
    name,
    "logo": {
      "_id": logo.asset->_id,
      "url": logo.asset->url
    },
    url,
    displayLocations,
    logoWidth,
    logoHeight,
    homepageOrder,
    aboutUsOrder
  }
`

export const getPartnerLogosQuery = groq`
  *[_type == "partnerLogo"] {
    _id,
    name,
    "logo": {
      "_id": logo.asset->_id,
      "url": logo.asset->url
    },
    url,
    logoWidth,
    logoHeight,
    displayLocations,
    homepageOrder,
    aboutUsOrder
  } | order(name asc)
`

export const getPartnerLogosByLocationQuery = groq`
  *[_type == "partnerLogo" && $location in displayLocations] {
    _id,
    name,
    "logo": {
      "_id": logo.asset->_id,
      "url": logo.asset->url
    },
    url,
    logoWidth,
    logoHeight,
    displayLocations,
    homepageOrder,
    aboutUsOrder
  } | order(name asc)
`

// New location-specific queries with proper numerical ordering
export const getHomepagePartnerLogosQuery = groq`
  *[_type == "partnerLogo" && "homepage" in displayLocations] {
    _id,
    name,
    "logo": {
      "_id": logo.asset->_id,
      "url": logo.asset->url
    },
    url,
    logoWidth,
    logoHeight,
    displayLocations,
    homepageOrder,
    aboutUsOrder
  } | order(coalesce(homepageOrder, 999999) asc, name asc)
`

export const getAboutUsPartnerLogosQuery = groq`
  *[_type == "partnerLogo" && "about-us" in displayLocations] {
    _id,
    name,
    "logo": {
      "_id": logo.asset->_id,
      "url": logo.asset->url
    },
    url,
    logoWidth,
    logoHeight,
    displayLocations,
    homepageOrder,
    aboutUsOrder
  } | order(coalesce(aboutUsOrder, 999999) asc, name asc)
`