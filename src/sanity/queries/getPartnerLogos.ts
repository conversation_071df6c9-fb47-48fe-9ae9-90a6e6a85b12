import { groq } from 'next-sanity'

export interface PartnerLogo {
  _id: string
  name: string
  logo: {
    _id: string
    url: string
  }
  url?: string
  displayLocations: string[]
  logoWidth?: number
  logoHeight?: number
  homepageOrder?: number
  aboutUsOrder?: number
}

export const getPartnerLogos = groq`
  *[_type == "partnerLogo"] {
    _id,
    name,
    "logo": {
      "_id": logo.asset->_id,
      "url": logo.asset->url
    },
    url,
    displayLocations,
    logoWidth,
    logoHeight
  }
`

export const getPartnerLogosQuery = groq`
  *[_type == "partnerLogo"] {
    _id,
    name,
    "logo": {
      "_id": logo.asset->_id,
      "url": logo.asset->url
    },
    url,
    logoWidth,
    logoHeight,
    displayLocations
  } | order(name asc)
`

export const getPartnerLogosByLocationQuery = groq`
  *[_type == "partnerLogo" && $location in displayLocations] {
    _id,
    name,
    "logo": {
      "_id": logo.asset->_id,
      "url": logo.asset->url
    },
    url,
    logoWidth,
    logoHeight,
    displayLocations
  } | order(name asc)
`