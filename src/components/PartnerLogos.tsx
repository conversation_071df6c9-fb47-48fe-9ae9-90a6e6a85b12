'use client'

import Image from 'next/image'
import { useCallback, useEffect, useState } from 'react'
import { useMedia<PERSON>uery, MEDIA_QUERY_TABLET, MEDIA_QUERY_DESKTOP } from '@/hooks/useMediaQuery'

interface PartnerLogo {
  _id: string
  logo: {
    _id: string
    url: string
  }
  name: string
  url?: string
  displayLocations?: string[]
  homepageOrder?: number
  aboutUsOrder?: number
}

interface PartnerLogosProps {
  logos: PartnerLogo[]
}

export function PartnerLogos({ logos = [] }: PartnerLogosProps) {
  // Filter out any logos with missing assets
  const validLogos = logos.filter(logo => logo && logo.logo)
  const [currentIndex, setCurrentIndex] = useState(0)
  const totalLogos = validLogos.length

  // Responsive logo count based on screen size
  const isDesktop = useMediaQuery(MEDIA_QUERY_DESKTOP)
  const isTablet = useMediaQuery(MEDIA_QUERY_TABLET)

  // 5 logos for desktop, 3 for tablet, 1 for mobile
  const logosPerPage = isDesktop ? 5 : (isTablet ? 3 : 1)

  const nextSlide = useCallback(() => {
    setCurrentIndex((prevIndex) =>
      (prevIndex + 1) % totalLogos
    )
  }, [totalLogos])

  const prevSlide = useCallback(() => {
    setCurrentIndex((prevIndex) =>
      prevIndex === 0 ? totalLogos - 1 : prevIndex - 1
    )
  }, [totalLogos])

  // Auto-advance the carousel every 5 seconds if there are more logos than can be displayed
  useEffect(() => {
    if (totalLogos <= logosPerPage) return

    const interval = setInterval(nextSlide, 5000)
    return () => clearInterval(interval)
  }, [nextSlide, totalLogos, logosPerPage, isDesktop, isTablet])

  // If no valid logos, don't render anything
  if (totalLogos === 0) {
    return null
  }

  // Get logos starting from the current index based on screen size (5 for desktop, 3 for tablet, 1 for mobile)
  const visibleLogos = Array.from({ length: Math.min(logosPerPage, totalLogos) }, (_, i) => {
    const index = (currentIndex + i) % totalLogos
    return validLogos[index]
  })

  return (
    <div className="w-full py-12 bg-white relative">
      <div className="container mx-auto px-4">
        <div className="relative">
          {totalLogos > logosPerPage && (
            <>
              <button
                onClick={prevSlide}
                className="absolute left-0 top-1/2 -translate-y-1/2 -translate-x-8 z-10 p-2 rounded-full bg-white shadow-lg hover:bg-gray-50 transition-colors"
                aria-label="Previous logo"
              >
                <svg
                  className="w-6 h-6 text-gray-600"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M15 19l-7-7 7-7"
                  />
                </svg>
              </button>
              <button
                onClick={nextSlide}
                className="absolute right-0 top-1/2 -translate-y-1/2 translate-x-8 z-10 p-2 rounded-full bg-white shadow-lg hover:bg-gray-50 transition-colors"
                aria-label="Next logo"
              >
                <svg
                  className="w-6 h-6 text-gray-600"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 5l7 7-7 7"
                  />
                </svg>
              </button>
            </>
          )}

          <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-4 md:gap-8 items-center justify-items-center">
            {visibleLogos.map((partner, index) => {
              if (!partner.logo?.url) {
                return null
              }

              const LogoWrapper = partner.url ? 'a' : 'div'
              const wrapperProps = partner.url ? {
                href: partner.url,
                target: '_blank',
                rel: 'noopener noreferrer',
                className: 'w-full h-auto aspect-[16/9] relative group transition-all duration-300 hover:opacity-80',
                'aria-label': `Visit ${partner.name}'s website`
              } : {
                className: 'w-full h-auto aspect-[16/9] relative group transition-all duration-300 hover:opacity-80'
              }

              return (
                <LogoWrapper
                  key={`${currentIndex}-${index}-${partner._id}`}
                  {...wrapperProps}
                >
                  <Image
                    src={partner.logo.url}
                    alt={partner.name}
                    fill
                    className="object-contain"
                    sizes="(max-width: 640px) 80px, (max-width: 768px) 120px, (max-width: 1024px) 160px, 200px"
                  />
                </LogoWrapper>
              )
            })}
          </div>

          {/* Navigation dots removed */}
        </div>
      </div>
    </div>
  )
}