'use client'

import Image from 'next/image'
import { useMemo } from 'react'

interface PartnerLogo {
  _id: string
  logo: {
    _id: string
    url: string
  }
  name: string
  url?: string
  logoWidth?: number
  logoHeight?: number
  aboutUsOrder?: number
}

interface AboutPartnerLogosProps {
  logos: PartnerLogo[]
  className?: string
}

// Fisher-Yates shuffle algorithm
function shuffleArray<T>(array: T[]): T[] {
  const shuffled = [...array]
  for (let i = shuffled.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1))
    ;[shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]]
  }
  return shuffled
}

export function AboutPartnerLogos({ logos = [], className = '' }: AboutPartnerLogosProps) {
  // Filter out any logos with missing assets
  const validLogos = logos.filter(logo => logo && logo.logo?.url)

  // Separate ordered and unordered logos, then randomize unordered ones
  const sortedLogos = useMemo(() => {
    const ordered = validLogos.filter(logo => typeof logo.aboutUsOrder === 'number')
    const unordered = validLogos.filter(logo => typeof logo.aboutUsOrder !== 'number')

    // Sort ordered logos numerically
    ordered.sort((a, b) => (a.aboutUsOrder || 0) - (b.aboutUsOrder || 0))

    // Randomize unordered logos
    const randomizedUnordered = shuffleArray(unordered)

    // Combine: ordered first, then randomized unordered
    return [...ordered, ...randomizedUnordered]
  }, [validLogos])

  if (sortedLogos.length === 0) {
    return null
  }

  return (
    <div className={`grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-x-10 gap-y-16 items-center justify-items-center ${className}`}>
      {validLogos.map((partner) => {
        const LogoWrapper = partner.url ? 'a' : 'div'
        const wrapperProps = partner.url ? {
          href: partner.url,
          target: '_blank',
          rel: 'noopener noreferrer',
          className: 'group w-full flex flex-col items-center',
          'aria-label': `Visit ${partner.name}'s website`
        } : {
          className: 'group w-full flex flex-col items-center',
          'aria-label': `${partner.name} logo`
        }

        return (
          <LogoWrapper
            key={partner._id}
            {...wrapperProps}
          >
            <div className="relative w-full aspect-[3/2] max-w-[250px] transition-transform duration-300 group-hover:scale-105">
              <Image
                src={partner.logo.url}
                alt={`${partner.name} logo`}
                fill
                className="object-contain"
                sizes="(max-width: 640px) 200px, (max-width: 768px) 220px, 250px"
                priority
              />
            </div>
          </LogoWrapper>
        )
      })}
    </div>
  )
}