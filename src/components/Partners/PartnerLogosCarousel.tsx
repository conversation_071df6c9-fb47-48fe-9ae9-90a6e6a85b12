'use client'

import Image from 'next/image'
import { useCallback, useEffect, useState } from 'react'
import { type Image as SanityImage } from 'sanity'
import { urlForImage } from '@/lib/sanity.image'

interface PartnerLogo {
  logo: SanityImage
  name: string
  url?: string
  homepageOrder?: number
  aboutUsOrder?: number
}

interface PartnerLogosCarouselProps {
  logos: PartnerLogo[]
  textColorClass: string
}

export function PartnerLogosCarousel({ logos, textColorClass }: PartnerLogosCarouselProps) {
  const [currentIndex, setCurrentIndex] = useState(0)
  const totalLogos = logos.length
  const logosPerPage = 5

  const nextSlide = useCallback(() => {
    setCurrentIndex((prevIndex) => 
      (prevIndex + 1) % totalLogos
    )
  }, [totalLogos])

  const prevSlide = useCallback(() => {
    setCurrentIndex((prevIndex) => 
      prevIndex === 0 ? totalLogos - 1 : prevIndex - 1
    )
  }, [totalLogos])

  // Auto-advance the carousel every 5 seconds if there are more than 5 logos
  useEffect(() => {
    if (totalLogos <= logosPerPage) return

    const interval = setInterval(nextSlide, 5000)
    return () => clearInterval(interval)
  }, [nextSlide, totalLogos, logosPerPage])

  // Get logos starting from the current index, wrapping around if needed
  const visibleLogos = Array.from({ length: logosPerPage }, (_, i) => {
    const index = (currentIndex + i) % totalLogos
    return logos[index]
  })

  return (
    <div className="relative">
      {totalLogos > logosPerPage && (
        <>
          <button
            onClick={prevSlide}
            className={`absolute left-0 top-1/2 -translate-y-1/2 -translate-x-8 z-10 p-2 rounded-full bg-white shadow-lg hover:bg-gray-50 transition-colors ${textColorClass}`}
            aria-label="Previous logo"
          >
            <svg
              className="w-6 h-6"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M15 19l-7-7 7-7"
              />
            </svg>
          </button>
          <button
            onClick={nextSlide}
            className={`absolute right-0 top-1/2 -translate-y-1/2 translate-x-8 z-10 p-2 rounded-full bg-white shadow-lg hover:bg-gray-50 transition-colors ${textColorClass}`}
            aria-label="Next logo"
          >
            <svg
              className="w-6 h-6"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M9 5l7 7-7 7"
              />
            </svg>
          </button>
        </>
      )}
      
      <div className="grid grid-cols-5 gap-8 items-center justify-items-center">
        {visibleLogos.map((partner, index) => {
          const imageUrl = urlForImage(partner.logo)?.url()

          if (!imageUrl) {
            return null
          }

          const LogoWrapper = partner.url ? 'a' : 'div'
          const wrapperProps = partner.url ? { 
            href: partner.url,
            target: '_blank',
            rel: 'noopener noreferrer'
          } : {}

          return (
            <LogoWrapper
              key={`${currentIndex}-${index}`}
              className="w-[200px] h-24 relative group transition-all duration-300 hover:opacity-80"
              {...wrapperProps}
            >
              <Image
                src={imageUrl}
                alt={partner.name}
                fill
                className="object-contain"
                sizes="200px"
              />
            </LogoWrapper>
          )
        })}
      </div>

      {totalLogos > logosPerPage && (
        <div className="flex justify-center mt-6 gap-2">
          {Array(totalLogos)
            .fill(null)
            .map((_, index) => (
              <button
                key={`dot-${index}`}
                onClick={() => setCurrentIndex(index)}
                className={`w-2 h-2 rounded-full transition-colors ${
                  index >= currentIndex && index < currentIndex + logosPerPage
                    ? textColorClass
                    : 'bg-gray-300'
                }`}
                aria-label={`Go to logo ${index + 1}`}
              />
            ))}
        </div>
      )}
    </div>
  )
} 