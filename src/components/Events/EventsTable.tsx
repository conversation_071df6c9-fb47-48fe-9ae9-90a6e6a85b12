'use client'

import { useState, useEffect, useMemo } from 'react'
import { ChevronUpIcon, ChevronDownIcon } from '@heroicons/react/24/outline'
import { useMediaQuery, MEDIA_QUERY_TABLET } from '@/hooks/useMediaQuery'

interface Event {
  id: string | number
  name: string
  venue: string
  state: string
  date: string
}

interface EventsTableProps {
  apiEndpoint: string
}

export default function EventsTable({ apiEndpoint }: EventsTableProps) {
  // Check if we're on tablet or larger screens
  const isTabletOrLarger = useMediaQuery(MEDIA_QUERY_TABLET)

  const [events, setEvents] = useState<Event[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1)
  const [entriesPerPage, setEntriesPerPage] = useState(10)
  // We track total entries for pagination UI
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [totalEntries, setTotalEntries] = useState(0)

  // Search state
  const [searchTerm, setSearchTerm] = useState('')

  // Sorting state
  const [sortField, setSortField] = useState<keyof Event>('date')
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc')

  // Format date to MM/DD/YYYY @ h:mm a format
  const formatEventDate = (dateString: string): string => {
    if (!dateString) return '';

    // Check if the date is already in the desired format
    if (dateString.includes('@')) {
      return dateString;
    }

    try {
      // Try to parse the date string
      let date: Date | null = null;

      // Handle ISO format (2025-04-03T17:45:00) or any valid date string
      date = new Date(dateString);

      // Check if date is valid
      if (isNaN(date.getTime())) {
        return dateString; // Return original if invalid
      }

      // Format as MM/DD/YYYY @ h:mm a
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const day = date.getDate().toString().padStart(2, '0');
      const year = date.getFullYear();

      let hours = date.getHours();
      const minutes = date.getMinutes().toString().padStart(2, '0');
      const ampm = hours >= 12 ? 'pm' : 'am';

      // Convert to 12-hour format
      hours = hours % 12;
      hours = hours ? hours : 12; // the hour '0' should be '12'

      return `${month}/${day}/${year} @ ${hours}:${minutes} ${ampm}`;
    } catch (error) {
      console.error('Error formatting date:', error);
      return dateString;
    }
  };

  // Fetch events data
  useEffect(() => {
    const fetchEvents = async () => {
      try {
        setLoading(true)

        // Check if we're using the external API
        const isExternalApi = apiEndpoint.includes('emsvending.com')

        if (isExternalApi) {
          // For external API, we need to use a proxy or handle CORS differently
          // Option 1: Use a proxy API route
          const response = await fetch('/api/proxy-events?url=' + encodeURIComponent(apiEndpoint))

          if (!response.ok) {
            throw new Error(`Failed to fetch events: ${response.status} ${response.statusText}`)
          }

          const data = await response.json()

          // Map the external API data format to our expected format
          // Adjust this mapping based on the actual API response structure
          const mappedEvents = Array.isArray(data)
            ? data.map((event: Record<string, unknown>) => ({
                id: event.id as string || Math.random().toString(36).slice(2, 11),
                name: (event.name as string) || (event.title as string) || (event.event_name as string) || '',
                venue: (event.venue as string) || (event.location as string) || (event.venue_name as string) || '',
                state: (event.state as string) || (event.region as string) || (event.venue_state as string) || '',
                date: formatEventDate((event.date as string) || (event.eventDate as string) || (event.event_date as string) || (event.start_time as string) || '')
              }))
            : data.events?.map((event: Record<string, unknown>) => ({
                id: event.id as string || Math.random().toString(36).slice(2, 11),
                name: (event.name as string) || (event.title as string) || (event.event_name as string) || '',
                venue: (event.venue as string) || (event.location as string) || (event.venue_name as string) || '',
                state: (event.state as string) || (event.region as string) || (event.venue_state as string) || '',
                date: formatEventDate((event.date as string) || (event.eventDate as string) || (event.event_date as string) || (event.start_time as string) || '')
              })) || []

          setEvents(mappedEvents)
          setTotalEntries(data.total || mappedEvents.length || 0)
        } else {
          // For local API
          const response = await fetch(apiEndpoint)

          if (!response.ok) {
            throw new Error(`Failed to fetch events: ${response.status} ${response.statusText}`)
          }

          const data = await response.json()
          setEvents(data.events || [])
          setTotalEntries(data.total || data.events?.length || 0)
        }

        setLoading(false)
      } catch (err) {
        console.error('Error fetching events:', err)
        setError(err instanceof Error ? err.message : 'Failed to fetch events')
        setLoading(false)

        // For development/demo purposes, load sample data if API fails
        loadSampleData()
      }
    }

    fetchEvents()
  }, [apiEndpoint])

  // Load sample data for development/demo purposes
  const loadSampleData = () => {
    const sampleData: Event[] = [
      { id: 1, name: 'VGK VS WINNIPEG', venue: 'T-Mobile Arena', state: 'NV', date: '04/03/2025 @ 5:45 pm' },
      { id: 2, name: 'Mariners vs. Giants', venue: 'Oracle Park', state: 'CA', date: '04/04/2025 @ 12:00 pm' },
      { id: 3, name: '2025 Major Arena Soccer League Playoffs', venue: 'Frontwave Arena', state: 'CA', date: '04/04/2025 @ 4:00 pm' },
      { id: 4, name: 'U.S.A. Vs Brazil WNT', venue: 'SOFI Stadium', state: 'CA', date: '04/05/2025 @ 10:00 am' },
      { id: 5, name: 'COLLEGE BASKETBALL CROWN', venue: 'T-Mobile Arena', state: 'NV', date: '04/05/2025 @ 12:00 pm' },
      { id: 6, name: 'Mariners vs. Giants', venue: 'Oracle Park', state: 'CA', date: '04/05/2025 @ 4:00 pm' },
      { id: 7, name: '2025 Major Arena Soccer League Playoffs', venue: 'Frontwave Arena', state: 'CA', date: '04/05/2025 @ 4:00 pm' },
      { id: 8, name: 'KELSEA BALLERINI BOK Center', venue: 'BOK Center', state: 'OK', date: '04/05/2025 @ 7:00 pm' },
      { id: 9, name: 'Atlanta United vs FC Dallas', venue: 'Mercedes-Benz Stadium', state: 'GA', date: '04/05/2025 @ 7:30 pm' },
      { id: 10, name: 'Mariners vs. Giants', venue: 'Oracle Park', state: 'CA', date: '04/06/2025 @ 11:30 am' },
      { id: 11, name: 'LA Lakers vs Golden State Warriors', venue: 'Crypto.com Arena', state: 'CA', date: '04/07/2025 @ 7:30 pm' },
      { id: 12, name: 'Taylor Swift | The Eras Tour', venue: 'Allegiant Stadium', state: 'NV', date: '04/08/2025 @ 7:00 pm' },
    ]

    setEvents(sampleData)
    setTotalEntries(sampleData.length)
    setError('Using sample data for demonstration')
  }

  // Handle search
  const filteredEvents = useMemo(() => {
    if (!searchTerm.trim()) return events

    const lowerCaseSearch = searchTerm.toLowerCase()
    return events.filter(event =>
      event.name.toLowerCase().includes(lowerCaseSearch) ||
      event.venue.toLowerCase().includes(lowerCaseSearch) ||
      event.state.toLowerCase().includes(lowerCaseSearch) ||
      event.date.toLowerCase().includes(lowerCaseSearch)
    )
  }, [events, searchTerm])

  // Parse date string for proper sorting
  const parseEventDate = (dateString: string): Date => {
    if (!dateString) return new Date(0) // Return epoch for invalid dates

    // Extract date part before @ symbol if present
    const datePart = dateString.split('@')[0].trim()

    // Try to parse MM/DD/YYYY format
    const dateMatch = datePart.match(/(\d{1,2})\/(\d{1,2})\/(\d{4})/)
    if (dateMatch) {
      const [, month, day, year] = dateMatch
      return new Date(parseInt(year), parseInt(month) - 1, parseInt(day))
    }

    // Fallback to standard Date parsing
    return new Date(dateString)
  }

  // Handle sorting
  const sortedEvents = useMemo(() => {
    const sorted = [...filteredEvents].sort((a, b) => {
      let aValue: string | number | Date = a[sortField]
      let bValue: string | number | Date = b[sortField]

      // Special handling for date field
      if (sortField === 'date') {
        aValue = parseEventDate(a.date)
        bValue = parseEventDate(b.date)
      }

      if (aValue < bValue) return sortDirection === 'asc' ? -1 : 1
      if (aValue > bValue) return sortDirection === 'asc' ? 1 : -1
      return 0
    })

    return sorted
  }, [filteredEvents, sortField, sortDirection])

  // Handle pagination
  const paginatedEvents = useMemo(() => {
    const startIndex = (currentPage - 1) * entriesPerPage
    return sortedEvents.slice(startIndex, startIndex + entriesPerPage)
  }, [sortedEvents, currentPage, entriesPerPage])

  // Calculate total pages
  const totalPages = Math.ceil(filteredEvents.length / entriesPerPage)

  // Handle sort toggle
  const handleSort = (field: keyof Event) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc')
    } else {
      setSortField(field)
      setSortDirection('asc')
    }
  }

  // Get header class based on sort field
  const getHeaderClass = (field: keyof Event) => {
    let baseClass = "px-4 py-3 text-left text-xs font-medium uppercase tracking-wider cursor-pointer bg-gray-100"

    // Add width classes based on the field
    if (field === 'name') {
      baseClass += " w-[40%]"
    } else if (field === 'venue') {
      baseClass += " w-[30%]"
    } else if (field === 'state') {
      baseClass += " w-[10%]"
    } else if (field === 'date') {
      baseClass += " w-[20%]"
    }

    return sortField === field
      ? `${baseClass} text-[#cd2653]`
      : `${baseClass} text-gray-800`
  }

  // Generate pagination numbers
  const paginationNumbers = () => {
    const pages = []
    const maxVisiblePages = 5

    if (totalPages <= maxVisiblePages) {
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i)
      }
    } else {
      // Always show first page
      pages.push(1)

      // Calculate start and end of visible pages
      let start = Math.max(2, currentPage - 1)
      let end = Math.min(totalPages - 1, currentPage + 1)

      // Adjust if we're near the beginning or end
      if (currentPage <= 2) {
        end = Math.min(totalPages - 1, 4)
      } else if (currentPage >= totalPages - 1) {
        start = Math.max(2, totalPages - 3)
      }

      // Add ellipsis if needed
      if (start > 2) {
        pages.push('...')
      }

      // Add middle pages
      for (let i = start; i <= end; i++) {
        pages.push(i)
      }

      // Add ellipsis if needed
      if (end < totalPages - 1) {
        pages.push('...')
      }

      // Always show last page
      pages.push(totalPages)
    }

    return pages
  }

  if (loading) {
    return (
      <div className="text-center py-10">
        <div className="inline-block h-8 w-8 animate-spin rounded-full border-4 border-solid border-current border-r-transparent align-[-0.125em] motion-reduce:animate-[spin_1.5s_linear_infinite]" role="status">
          <span className="!absolute !-m-px !h-px !w-px !overflow-hidden !whitespace-nowrap !border-0 !p-0 ![clip:rect(0,0,0,0)]">Loading...</span>
        </div>
        <p className="mt-2 text-gray-600">Loading events...</p>
      </div>
    )
  }

  // Render mobile card view
  const renderMobileView = () => {
    return (
      <div className="w-full">
        {/* Search input for mobile */}
        <div className="mb-4">
          <div className="flex items-center">
            <label htmlFor="mobile-search" className="sr-only">Search events</label>
            <input
              id="mobile-search"
              type="text"
              className="border border-gray-300 rounded-lg px-4 py-2 w-full focus:border-[#cd2653] focus:ring-[#cd2653]"
              value={searchTerm}
              onChange={(e) => {
                setSearchTerm(e.target.value)
                setCurrentPage(1) // Reset to first page when searching
              }}
              placeholder="Search events..."
            />
          </div>
        </div>

        {/* Error message */}
        {error && (
          <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-4">
            <p className="text-sm text-yellow-700">{error}</p>
          </div>
        )}

        {/* Sort options for mobile */}
        <div className="mb-4">
          <label htmlFor="mobile-sort" className="block text-sm font-medium text-gray-700 mb-1">Sort by:</label>
          <select
            id="mobile-sort"
            className="border border-gray-300 rounded-lg px-4 py-2 w-full focus:border-[#cd2653] focus:ring-[#cd2653]"
            value={`${sortField}-${sortDirection}`}
            onChange={(e) => {
              const [field, direction] = e.target.value.split('-') as [keyof Event, 'asc' | 'desc']
              setSortField(field)
              setSortDirection(direction)
            }}
          >
            <option value="name-asc">Name (A-Z)</option>
            <option value="name-desc">Name (Z-A)</option>
            <option value="venue-asc">Venue (A-Z)</option>
            <option value="venue-desc">Venue (Z-A)</option>
            <option value="state-asc">State (A-Z)</option>
            <option value="state-desc">State (Z-A)</option>
            <option value="date-asc">Date (Earliest first)</option>
            <option value="date-desc">Date (Latest first)</option>
          </select>
        </div>

        {/* Event cards */}
        <div className="space-y-4">
          {paginatedEvents.length > 0 ? (
            paginatedEvents.map((event) => (
              <div key={event.id} className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 hover:shadow-md transition-shadow duration-200">
                <h3 className="font-medium text-gray-900 text-lg mb-3">{event.name}</h3>
                <div className="grid grid-cols-1 gap-3 text-sm">
                  <div className="flex items-start">
                    <div className="w-20 flex-shrink-0">
                      <p className="text-gray-500 font-medium">Venue:</p>
                    </div>
                    <p className="text-gray-900 flex-grow">{event.venue}</p>
                  </div>
                  <div className="flex items-start">
                    <div className="w-20 flex-shrink-0">
                      <p className="text-gray-500 font-medium">State:</p>
                    </div>
                    <p className="text-gray-900 flex-grow">{event.state}</p>
                  </div>
                  <div className="flex items-start">
                    <div className="w-20 flex-shrink-0">
                      <p className="text-gray-500 font-medium">Date:</p>
                    </div>
                    <p className="text-gray-900 flex-grow">{event.date}</p>
                  </div>
                </div>
              </div>
            ))
          ) : (
            <div className="text-center py-8 bg-white rounded-lg shadow-sm border border-gray-200">
              <p className="text-gray-500">No events found</p>
            </div>
          )}
        </div>

        {/* Mobile pagination */}
        <div className="mt-6 flex flex-col items-center">
          <div className="text-sm text-gray-700 mb-4">
            Showing {filteredEvents.length > 0 ? (currentPage - 1) * entriesPerPage + 1 : 0} to {Math.min(currentPage * entriesPerPage, filteredEvents.length)} of {filteredEvents.length} entries
          </div>

          <div className="flex items-center justify-center space-x-2">
            <button
              onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
              disabled={currentPage === 1}
              className="px-4 py-2 rounded-md text-sm font-medium text-white bg-[#cd2653] hover:bg-[#b32147] disabled:opacity-50 disabled:bg-gray-300 disabled:text-gray-500 disabled:cursor-not-allowed"
              aria-label="Previous page"
            >
              Previous
            </button>
            <span className="text-sm text-gray-700 font-medium">
              Page {currentPage} of {totalPages || 1}
            </span>
            <button
              onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
              disabled={currentPage === totalPages || totalPages === 0}
              className="px-4 py-2 rounded-md text-sm font-medium text-white bg-[#cd2653] hover:bg-[#b32147] disabled:opacity-50 disabled:bg-gray-300 disabled:text-gray-500 disabled:cursor-not-allowed"
              aria-label="Next page"
            >
              Next
            </button>
          </div>

          {/* Entries per page selector */}
          <div className="mt-4 flex items-center">
            <label htmlFor="mobile-entries-per-page" className="text-sm text-gray-700 mr-2">Show:</label>
            <select
              id="mobile-entries-per-page"
              className="border border-gray-300 rounded px-2 py-1 text-sm"
              value={entriesPerPage}
              onChange={(e) => {
                setEntriesPerPage(Number(e.target.value))
                setCurrentPage(1) // Reset to first page when changing entries per page
              }}
            >
              {[10, 25, 50, 100].map(value => (
                <option key={value} value={value}>{value}</option>
              ))}
            </select>
            <span className="ml-2 text-sm text-gray-700">entries</span>
          </div>
        </div>
      </div>
    )
  }

  // Render desktop table view
  const renderDesktopView = () => {
    return (
      <div className="w-full">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-4 gap-4">
          <div className="flex items-center">
            <label htmlFor="entries-per-page" className="sr-only">Select number of entries per page</label>
            <select
              id="entries-per-page"
              aria-label="Number of entries per page"
              className="border border-gray-300 rounded px-2 py-1 text-sm"
              value={entriesPerPage}
              onChange={(e) => {
                setEntriesPerPage(Number(e.target.value))
                setCurrentPage(1) // Reset to first page when changing entries per page
              }}
            >
              {[10, 25, 50, 100].map(value => (
                <option key={value} value={value}>{value}</option>
              ))}
            </select>
            <span className="ml-2 text-sm">entries per page</span>
          </div>

          <div className="w-full md:w-auto">
            <div className="flex items-center">
              <label htmlFor="search" className="mr-2 text-sm">Search:</label>
              <input
                id="search"
                type="text"
                className="border border-gray-300 rounded px-3 py-1 w-full md:w-64"
                value={searchTerm}
                onChange={(e) => {
                  setSearchTerm(e.target.value)
                  setCurrentPage(1) // Reset to first page when searching
                }}
                placeholder="Search events..."
              />
            </div>
          </div>
        </div>

        {error && (
          <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-4">
            <div className="flex">
              <div className="ml-3">
                <p className="text-sm text-yellow-700">
                  {error}
                </p>
              </div>
            </div>
          </div>
        )}

        <div className="overflow-x-auto border border-gray-200 rounded-sm">
          <table className="w-full divide-y divide-gray-200 table-fixed">
            <thead className="bg-gray-50">
              <tr>
                <th
                  scope="col"
                  className={`${getHeaderClass('name')}`}
                  onClick={() => handleSort('name')}
                >
                  <div className="flex items-center">
                    Name
                    <span className="ml-1">
                      {sortField === 'name' ? (
                        sortDirection === 'asc' ? (
                          <ChevronUpIcon className="h-4 w-4 text-[#cd2653]" />
                        ) : (
                          <ChevronDownIcon className="h-4 w-4 text-[#cd2653]" />
                        )
                      ) : (
                        <span className="h-4 w-4 inline-block"></span>
                      )}
                    </span>
                  </div>
                </th>
                <th
                  scope="col"
                  className={getHeaderClass('venue')}
                  onClick={() => handleSort('venue')}
                >
                  <div className="flex items-center">
                    Venue
                    <span className="ml-1">
                      {sortField === 'venue' ? (
                        sortDirection === 'asc' ? (
                          <ChevronUpIcon className="h-4 w-4 text-[#cd2653]" />
                        ) : (
                          <ChevronDownIcon className="h-4 w-4 text-[#cd2653]" />
                        )
                      ) : (
                        <span className="h-4 w-4 inline-block"></span>
                      )}
                    </span>
                  </div>
                </th>
                <th
                  scope="col"
                  className={getHeaderClass('state')}
                  onClick={() => handleSort('state')}
                >
                  <div className="flex items-center">
                    State
                    <span className="ml-1">
                      {sortField === 'state' ? (
                        sortDirection === 'asc' ? (
                          <ChevronUpIcon className="h-4 w-4 text-[#cd2653]" />
                        ) : (
                          <ChevronDownIcon className="h-4 w-4 text-[#cd2653]" />
                        )
                      ) : (
                        <span className="h-4 w-4 inline-block"></span>
                      )}
                    </span>
                  </div>
                </th>
                <th
                  scope="col"
                  className={getHeaderClass('date')}
                  onClick={() => handleSort('date')}
                >
                  <div className="flex items-center">
                    Date
                    <span className="ml-1">
                      {sortField === 'date' ? (
                        sortDirection === 'asc' ? (
                          <ChevronUpIcon className="h-4 w-4 text-[#cd2653]" />
                        ) : (
                          <ChevronDownIcon className="h-4 w-4 text-[#cd2653]" />
                        )
                      ) : (
                        <span className="h-4 w-4 inline-block"></span>
                      )}
                    </span>
                  </div>
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200 text-sm">
              {paginatedEvents.length > 0 ? (
                paginatedEvents.map((event) => (
                  <tr key={event.id} className="hover:bg-gray-50 even:bg-gray-50">
                    <td className="px-4 py-3 text-sm font-medium text-gray-900"><div className="truncate max-w-full">{event.name}</div></td>
                    <td className="px-4 py-3 text-sm text-gray-500"><div className="truncate max-w-full">{event.venue}</div></td>
                    <td className="px-4 py-3 text-sm text-gray-500 text-left">{event.state}</td>
                    <td className="px-4 py-3 text-sm text-gray-500"><div className="truncate max-w-full">{event.date}</div></td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={4} className="px-4 py-3 text-center text-sm text-gray-500">
                    No events found
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>

        <div className="flex flex-col md:flex-row justify-between items-center mt-4">
          <div className="text-sm text-gray-700 mb-4 md:mb-0">
            Showing {filteredEvents.length > 0 ? (currentPage - 1) * entriesPerPage + 1 : 0} to {Math.min(currentPage * entriesPerPage, filteredEvents.length)} of {filteredEvents.length} entries
          </div>

          <div className="flex items-center space-x-1">
            <button
              onClick={() => setCurrentPage(1)}
              disabled={currentPage === 1}
              className="px-3 py-1 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              aria-label="First page"
            >
              &laquo;
            </button>
            <button
              onClick={() => setCurrentPage(currentPage - 1)}
              disabled={currentPage === 1}
              className="px-3 py-1 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              aria-label="Previous page"
            >
              &lsaquo;
            </button>

            {paginationNumbers().map((page, index) => (
              typeof page === 'number' ? (
                <button
                  key={index}
                  onClick={() => setCurrentPage(page)}
                  className={`px-3 py-1 border ${currentPage === page ? 'bg-blue-50 border-blue-500 text-blue-600' : 'border-gray-300 text-gray-700 hover:bg-gray-50'} rounded-md text-sm font-medium`}
                >
                  {page}
                </button>
              ) : (
                <span key={index} className="px-2 py-1 text-gray-500">
                  {page}
                </span>
              )
            ))}

            <button
              onClick={() => setCurrentPage(currentPage + 1)}
              disabled={currentPage === totalPages || totalPages === 0}
              className="px-3 py-1 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              aria-label="Next page"
            >
              &rsaquo;
            </button>
            <button
              onClick={() => setCurrentPage(totalPages)}
              disabled={currentPage === totalPages || totalPages === 0}
              className="px-3 py-1 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              aria-label="Last page"
            >
              &raquo;
            </button>
          </div>
        </div>
      </div>
    )
  }

  // Conditionally render based on screen size
  return (
    <div className="w-full">
      {isTabletOrLarger ? renderDesktopView() : renderMobileView()}
    </div>
  )
}
