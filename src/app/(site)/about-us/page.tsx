import { Metadata } from 'next'
import Image from 'next/image'
import Link from 'next/link'
import { sanityFetch } from '@/sanity/lib/live'
import { HOME_QUERY } from '@/sanity/lib/queries'
import { ABOUT_US_QUERY, type AboutUsPage } from '@/sanity/queries/getAboutUs'
import { urlFor } from '@/sanity/lib/image'
import { AboutPartnerLogos } from '@/components/Partners/AboutPartnerLogos'
import { getAboutUsPartnerLogosQuery } from '@/sanity/queries/getPartnerLogos'
import { PortableText } from '@portabletext/react'
import VideoPlayer from '@/components/VideoPlayer'

export async function generateMetadata(): Promise<Metadata> {
  const { data: aboutUs } = await sanityFetch({
    query: ABOUT_US_QUERY,
    tags: ['aboutUs']
  })

  return {
    title: aboutUs?.seo?.title ?? 'About Us - EMS Vending',
    description: aboutUs?.seo?.description ?? 'Leading foodservice providers for stadiums, arenas, NASCAR tracks, and concert venues.',
    openGraph: {
      title: aboutUs?.seo?.title ?? 'About Us - EMS Vending',
      description: aboutUs?.seo?.description ?? 'Leading foodservice providers for stadiums, arenas, NASCAR tracks, and concert venues.',
      images: aboutUs?.seo?.ogImage ? [urlFor(aboutUs.seo.ogImage).url()] : ['/images/meta/fallback-og-image.jpg'],
    },
    twitter: {
      card: 'summary_large_image',
      title: aboutUs?.seo?.title ?? 'About Us - EMS Vending',
      description: aboutUs?.seo?.description ?? 'Leading foodservice providers for stadiums, arenas, NASCAR tracks, and concert venues.',
      images: aboutUs?.seo?.ogImage ? [urlFor(aboutUs.seo.ogImage).url()] : ['/images/meta/fallback-og-image.jpg'],
    },
  }
}

export default async function AboutUsPage() {
  const [{ data: home }, { data: aboutUs }, { data: partnerLogos }] = await Promise.all([
    sanityFetch({
      query: HOME_QUERY,
      tags: ['home']
    }),
    sanityFetch({
      query: ABOUT_US_QUERY,
      tags: ['aboutUs']
    }),
    sanityFetch({
      query: getAboutUsPartnerLogosQuery,
      tags: ['partnerLogo']
    })
  ])

  const aboutUsLogos = Array.isArray(partnerLogos) ? partnerLogos : []

  return (
    <div className="flex flex-col min-h-screen">
      {/* Hero Section */}
      <section className="relative py-20 flex items-center justify-center bg-gray-50 border-b border-gray-200">
        <div className="text-center text-black max-w-4xl mx-auto px-4">
          <h1 className="text-[56px] font-bold leading-[1.1] mb-4">
            {aboutUs?.hero?.title ?? 'About Us'}
          </h1>
        </div>
      </section>

      {/* Industry Leader Section */}
      <section className="pt-12 pb-16">
        <div className="site-container site-max-w grid grid-cols-1 md:grid-cols-12 gap-8 md:gap-12 md:items-stretch">
          {/* Video - 66.666% (8/12) */}
          <div className="md:col-span-8 md:order-2 h-full">
            {aboutUs?.industryLeader?.videoUrl ? (
              <VideoPlayer
                videoUrl={aboutUs.industryLeader.videoUrl}
                thumbnailImage={aboutUs.industryLeader.videoThumbnail}
                title={aboutUs.industryLeader.title ?? 'Industry Leader'}
              />
            ) : (
              <div className="relative aspect-video rounded-lg overflow-hidden">
                <Image
                  src="/images/events-video-placeholder.jpg"
                  alt="Event Management Solutions Video"
                  fill
                  className="object-cover"
                />
              </div>
            )}
          </div>

          {/* Text - 33.333% (4/12) */}
          <div className="md:col-span-4 md:order-1 flex flex-col justify-center h-full">
            <h2 className="text-3xl font-bold mb-6">
              {aboutUs?.industryLeader?.title ?? 'Industry Leader'}
            </h2>
            <p className="text-lg text-gray-700">
              {aboutUs?.industryLeader?.description ??
                'Event Management Solutions is the industry leader for managing the in-seat vending operation and/or providing concessions staffing support for stadiums, arenas, NASCAR tracks and concert venues.'}
            </p>
          </div>
        </div>
      </section>

      {/* Venues Section */}
      <section className="py-20">
        <div className="site-container site-max-w">
          <h2 className="text-center mb-20" style={{ fontSize: '56px', fontWeight: 700, lineHeight: 1.1 }}>Venues</h2>
          {aboutUsLogos.length > 0 && (
            <>
              <AboutPartnerLogos logos={aboutUsLogos} />
              <div className="text-center mt-16">
                <h3 className="text-2xl font-medium">And Many More...</h3>
              </div>
            </>
          )}
        </div>
      </section>

      {/* What Does It Pay? Section */}
      <section className="py-16">
        <div className="site-container site-max-w text-center">
          <h2 className="text-3xl font-bold mb-8">
            {aboutUs?.whatDoesItPay?.title ?? 'What Does It Pay?'}
          </h2>
          {aboutUs?.whatDoesItPay?.content && (
            <div className="prose prose-lg mx-auto">
              <PortableText value={aboutUs.whatDoesItPay.content} />
            </div>
          )}
        </div>
      </section>

      {/* Let's Talk Section */}
      {home?.letsTalk && (
        <section className="relative py-20 bg-fixed bg-cover bg-center h-[60vh] flex items-center justify-center overflow-hidden"
          style={{
            backgroundImage: home.letsTalk.backgroundImage ? `url(${urlFor(home.letsTalk.backgroundImage).url()})` : undefined,
            backgroundAttachment: 'fixed'
          }}>
          <div className="absolute inset-0 bg-black/50" />
          <div className="site-container site-max-w text-center text-white relative z-10">
            <h2 className="text-3xl font-bold mb-4">Let&apos;s Talk</h2>
            <p className="text-xl mb-8">Contact us today to learn more about becoming a vendor or how EMS can serve your venue.</p>
            {home.letsTalk.buttonLink && (
              <Link
                href={home.letsTalk.buttonLink}
                className="inline-block bg-[#cd2653] text-white px-8 py-3 rounded-full hover:bg-[#cd2653]/90 transition-colors uppercase font-medium"
              >
                CONTACT US
              </Link>
            )}
          </div>
        </section>
      )}
    </div>
  )
}